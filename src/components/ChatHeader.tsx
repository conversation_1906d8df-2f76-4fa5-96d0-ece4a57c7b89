import { useState } from 'react';
import { ArrowLeft, MoreVertical, Phone, Video, Info } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface ChatHeaderProps {
    characterName: string;
    characterAvatar?: string;
    isOnline?: boolean;
    lastSeen?: Date;
    onBack?: () => void;
}

const ChatHeader = ({ 
    characterName, 
    characterAvatar, 
    isOnline = true, 
    lastSeen,
    onBack 
}: ChatHeaderProps) => {
    const [imageError, setImageError] = useState(false);
    const router = useRouter();

    const handleBack = () => {
        if (onBack) {
            onBack();
        } else {
            router.back();
        }
    };

    const getAvatarPlaceholder = (name: string) => {
        return name ? name.charAt(0).toUpperCase() : 'A';
    };

    const getStatusText = () => {
        if (isOnline) {
            return 'Online';
        } else if (lastSeen) {
            const now = new Date();
            const diffMs = now.getTime() - lastSeen.getTime();
            const diffMins = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffMins < 1) return 'Just now';
            if (diffMins < 60) return `${diffMins}m ago`;
            if (diffHours < 24) return `${diffHours}h ago`;
            return `${diffDays}d ago`;
        }
        return 'Offline';
    };

    return (
        <div className="bg-gray-900/95 backdrop-blur-sm border-b border-gray-700/50 px-4 py-3 shadow-lg">
            <div className="max-w-4xl mx-auto flex items-center justify-between">
                {/* Left Section */}
                <div className="flex items-center space-x-3">
                    {/* Back Button */}
                    <button
                        onClick={handleBack}
                        className="p-2 text-gray-400 hover:text-gray-300 hover:bg-gray-800/50 rounded-full transition-all duration-200 lg:hidden"
                        title="Go back"
                    >
                        <ArrowLeft size={20} />
                    </button>

                    {/* Character Avatar */}
                    <div className="relative">
                        <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
                            {characterAvatar && !imageError ? (
                                <img 
                                    src={characterAvatar} 
                                    alt={characterName} 
                                    className="w-full h-full object-cover"
                                    onError={() => setImageError(true)}
                                />
                            ) : (
                                <span className="text-white font-semibold text-lg">
                                    {getAvatarPlaceholder(characterName)}
                                </span>
                            )}
                        </div>
                        
                        {/* Online Status Indicator */}
                        <div className={`absolute -bottom-0.5 -right-0.5 w-4 h-4 rounded-full border-2 border-gray-900 ${
                            isOnline ? 'bg-green-500' : 'bg-gray-500'
                        }`}></div>
                    </div>

                    {/* Character Info */}
                    <div className="flex flex-col">
                        <h1 className="text-lg font-semibold text-gray-100 leading-tight">
                            {characterName}
                        </h1>
                        <p className={`text-sm leading-tight ${
                            isOnline ? 'text-green-400' : 'text-gray-400'
                        }`}>
                            {getStatusText()}
                        </p>
                    </div>
                </div>

                {/* Right Section - Action Buttons */}
                <div className="flex items-center space-x-2">
                    {/* Voice Call Button */}
                    <button
                        className="p-2 text-gray-400 hover:text-gray-300 hover:bg-gray-800/50 rounded-full transition-all duration-200"
                        title="Voice call"
                    >
                        <Phone size={20} />
                    </button>

                    {/* Video Call Button */}
                    <button
                        className="p-2 text-gray-400 hover:text-gray-300 hover:bg-gray-800/50 rounded-full transition-all duration-200"
                        title="Video call"
                    >
                        <Video size={20} />
                    </button>

                    {/* Info Button */}
                    <button
                        className="p-2 text-gray-400 hover:text-gray-300 hover:bg-gray-800/50 rounded-full transition-all duration-200"
                        title="Character info"
                    >
                        <Info size={20} />
                    </button>

                    {/* More Options */}
                    <button
                        className="p-2 text-gray-400 hover:text-gray-300 hover:bg-gray-800/50 rounded-full transition-all duration-200"
                        title="More options"
                    >
                        <MoreVertical size={20} />
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ChatHeader;
