import { useState } from 'react';

interface TypingIndicatorProps {
    characterName?: string;
    characterAvatar?: string;
    isVisible: boolean;
}

const TypingIndicator = ({ characterName, characterAvatar, isVisible }: TypingIndicatorProps) => {
    const [imageError, setImageError] = useState(false);

    const getAvatarPlaceholder = (name: string) => {
        return name ? name.charAt(0).toUpperCase() : 'A';
    };

    if (!isVisible) return null;

    return (
        <div className="flex justify-start w-full mb-4 animate-fade-in">
            {/* AI Avatar */}
            <div className="flex-shrink-0 mr-3">
                <div className="w-10 h-10 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
                    {characterAvatar && !imageError ? (
                        <img 
                            src={characterAvatar} 
                            alt={characterName || 'AI'} 
                            className="w-full h-full object-cover"
                            onError={() => setImageError(true)}
                        />
                    ) : (
                        <span className="text-white font-semibold text-sm">
                            {getAvatarPlaceholder(characterName || 'AI')}
                        </span>
                    )}
                </div>
            </div>
            
            {/* Typing Bubble */}
            <div className="flex flex-col items-start max-w-[75%] sm:max-w-[65%]">
                <div className="bg-gradient-to-br from-gray-700 to-gray-800 text-white rounded-2xl rounded-bl-md border border-gray-600/30 px-4 py-3 shadow-lg backdrop-blur-sm">
                    <div className="flex items-center space-x-1">
                        <div className="flex space-x-1">
                            <div 
                                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" 
                                style={{ animationDelay: '0ms', animationDuration: '1.4s' }}
                            ></div>
                            <div 
                                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" 
                                style={{ animationDelay: '0.2s', animationDuration: '1.4s' }}
                            ></div>
                            <div 
                                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" 
                                style={{ animationDelay: '0.4s', animationDuration: '1.4s' }}
                            ></div>
                        </div>
                        <span className="text-xs text-gray-400 ml-2">
                            {characterName || 'AI'} is typing...
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TypingIndicator;
