"use client"

import { signIn } from "next-auth/react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { FormEvent, useState } from "react"

export default function RegisterPage() {
    const router = useRouter()
    const [error, setError] = useState<string>("")
    const [isLoading, setIsLoading] = useState(false)

    async function onSubmit(event: FormEvent<HTMLFormElement>) {
        event.preventDefault()
        setIsLoading(true)
        setError("")

        const formData = new FormData(event.currentTarget)
        const name = formData.get("name") as string
        const email = formData.get("email") as string
        const password = formData.get("password") as string

        try {
            const response = await fetch("/api/auth/register", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ name, email, password }),
            })

            if (!response.ok) {
                const data = await response.json()
                throw new Error(data.error || "Failed to register")
            }

            // After successful registration, sign in automatically
            const result = await signIn("credentials", {
                email,
                password,
                redirect: false,
            })

            if (result?.error) {
                setError("Registration successful but failed to sign in")
                return
            }

            router.push("/")
            router.refresh()
        } catch (error) {
            setError(error instanceof Error ? error.message : "Something went wrong")
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <div className="flex items-center justify-center min-h-[calc(100vh-60px)] md:min-h-screen p-4">
            <div className="w-full max-w-sm space-y-6 bg-zinc-900/50 backdrop-blur-xl p-6 md:p-8 rounded-lg border border-zinc-800">
                <div className="space-y-2 text-center">
                    <h1 className="text-xl md:text-2xl font-bold tracking-tight">Create an account</h1>
                    <p className="text-sm text-zinc-400">Enter your details to get started</p>
                </div>
                <form className="space-y-4" onSubmit={onSubmit}>
                    <div className="space-y-2">
                        <label
                            htmlFor="name"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                            Name
                        </label>
                        <input
                            id="name"
                            name="name"
                            type="text"
                            placeholder="John Doe"
                            required
                            className="flex h-9 w-full rounded-md border border-zinc-800 bg-zinc-950/50 px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-zinc-400 focus:outline-none focus:ring-1 focus:ring-zinc-400 focus:border-zinc-700"
                        />
                    </div>
                    <div className="space-y-2">
                        <label
                            htmlFor="email"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                            Email
                        </label>
                        <input
                            id="email"
                            name="email"
                            type="email"
                            placeholder="<EMAIL>"
                            required
                            className="flex h-9 w-full rounded-md border border-zinc-800 bg-zinc-950/50 px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-zinc-400 focus:outline-none focus:ring-1 focus:ring-zinc-400 focus:border-zinc-700"
                        />
                    </div>
                    <div className="space-y-2">
                        <label
                            htmlFor="password"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                            Password
                        </label>
                        <input
                            id="password"
                            name="password"
                            type="password"
                            required
                            minLength={6}
                            className="flex h-9 w-full rounded-md border border-zinc-800 bg-zinc-950/50 px-3 py-1 text-sm shadow-sm transition-colors placeholder:text-zinc-400 focus:outline-none focus:ring-1 focus:ring-zinc-400 focus:border-zinc-700"
                        />
                    </div>
                    <button
                        type="submit"
                        className="w-full px-4 py-2 text-white bg-primary rounded-lg hover:bg-primary/90 disabled:opacity-50"
                        disabled={isLoading}
                    >
                        {isLoading ? "Creating account..." : "Create account"}
                    </button>
                    {error && (
                        <p className="text-sm text-red-500 text-center">{error}</p>
                    )}
                </form>
                <div className="text-center text-sm">
                    <Link href="/login" className="text-zinc-400 hover:text-zinc-50 hover:underline underline-offset-4">
                        Already have an account? Sign in
                    </Link>
                </div>
            </div>
        </div>
    )
}
