{"name": "ooaa-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@prisma/client": "6.2.0", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@tailwindcss/typography": "0.5.16", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "0.544.0", "next": "^15.5.3", "next-auth": "^4.24.11", "next-intl": "^4.3.8", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "^15.5.3", "postcss": "^8", "prisma": "6.2.0", "tailwindcss": "^3.4.1", "typescript": "^5", "vitest": "^2.1.8"}}